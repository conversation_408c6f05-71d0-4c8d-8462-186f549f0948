import { GraduationCap, Heart, Target, Code } from "lucide-react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function About() {
  const values = [
    {
      icon: Code,
      title: "Clean Code",
      description: "I believe in writing maintainable, readable code that stands the test of time."
    },
    {
      icon: Heart,
      title: "User-Centric",
      description: "Every line of code I write is with the end user's experience in mind."
    },
    {
      icon: Target,
      title: "Goal-Oriented",
      description: "I set clear objectives and work systematically to achieve them."
    },
    {
      icon: GraduationCap,
      title: "Continuous Learning",
      description: "Technology evolves rapidly, and I'm committed to growing with it."
    }
  ]

  const interests = [
    "Web Development",
    "UI/UX Design",
    "Open Source",
    "Tech Communities",
    "Problem Solving",
    "Innovation"
  ]

  return (
    <div className="container mx-auto px-6 py-8 max-w-4xl">
      {/* Hero Section */}
      <div className="space-y-6 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold">About Me</h1>
        <p className="text-xl text-muted-foreground">
          From Computer Engineering graduate to passionate Frontend Developer
        </p>
      </div>

      {/* Personal Story */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GraduationCap className="w-5 h-5 text-primary" />
            My Journey
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground leading-relaxed">
            I graduated with a Computer Engineering degree in May 2024, but my journey into frontend development 
            began during my final year when I discovered the perfect blend of technical challenge and creative 
            expression that web development offers.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            What started as curiosity about how websites work evolved into a deep passion for creating digital 
            experiences that matter. I found myself drawn to the immediate feedback loop of frontend development – 
            the ability to see your code come to life visually and interact with users in real-time.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            Since graduation, I've immersed myself in modern web technologies, focusing on React, TypeScript, and 
            contemporary UI frameworks. Every project teaches me something new, and I'm excited about the endless 
            possibilities in this field.
          </p>
        </CardContent>
      </Card>

      {/* What Excites Me */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-primary" />
            What Excites Me About Coding
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground leading-relaxed">
            There's something magical about transforming ideas into interactive experiences. I love the problem-solving 
            aspect of development – breaking down complex challenges into manageable pieces and finding elegant solutions.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            The frontend development community is incredibly welcoming and collaborative. I'm constantly inspired by 
            the innovations and sharing of knowledge within this ecosystem. Contributing to open-source projects and 
            learning from other developers has become a significant part of my growth journey.
          </p>
        </CardContent>
      </Card>

      {/* Career Goals */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5 text-primary" />
            Career Goals & Aspirations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground leading-relaxed">
            My immediate goal is to join a team where I can contribute to meaningful projects while continuing to 
            learn from experienced developers. I'm particularly interested in companies that value code quality, 
            user experience, and innovation.
          </p>
          <p className="text-muted-foreground leading-relaxed">
            Long-term, I aspire to become a senior frontend developer who can mentor others and lead projects that 
            make a positive impact. I'm also interested in exploring the intersection of frontend development with 
            emerging technologies like AI and machine learning.
          </p>
        </CardContent>
      </Card>

      {/* Professional Values */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-6">Professional Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {values.map(({ icon: Icon, title, description }) => (
            <Card key={title}>
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Personal Interests */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Interests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {interests.map((interest) => (
              <Badge key={interest} variant="secondary">
                {interest}
              </Badge>
            ))}
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            When I'm not coding, I enjoy staying updated with the latest tech trends, participating in developer 
            communities, and working on side projects that challenge me to learn new skills.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}