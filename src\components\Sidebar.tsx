import { Download, Mail } from "lucide-react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Gith<PERSON> } from "react-icons/si"
const profileImage = "/lovable-uploads/83300ab6-dedc-4701-80f8-b921caa2e739.png"

export function Sidebar() {
  return (
    <div className="fixed left-0 top-0 z-30 h-screen w-80 border-r border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/60 hidden lg:block">
      <div className="absolute inset-0 bg-gradient-to-b from-background/90 via-background/95 to-background opacity-60"></div>
      <div className="relative flex h-full flex-col justify-between p-10">
        {/* Header Section */}
        <div className="space-y-10">
          {/* Avatar */}
          <div className="flex justify-center">
            <div className="relative group">
              <div className="absolute -inset-0.5 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
              <div className="relative">
                <img
                  src={profileImage}
                  alt="CJ Jutba"
                  className="w-32 h-32 rounded-full object-cover shadow-xl border-2 border-background/50 group-hover:shadow-2xl transition-all duration-300"
                />
                <div className="absolute bottom-1 right-1 bg-emerald-500 w-6 h-6 rounded-full border-2 border-background shadow-lg">
                  <div className="w-full h-full bg-emerald-400 rounded-full animate-pulse opacity-75"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Name & Title */}
          <div className="text-center space-y-4">
            <h1 className="text-5xl font-extrabold tracking-wide text-slate-800 dark:text-slate-100">
              CJ Jutba
            </h1>
            <div className="space-y-3">
              <p className="text-xl font-bold text-slate-600 dark:text-slate-300">Frontend Developer</p>
              <div className="h-px w-16 bg-gradient-to-r from-transparent via-primary/60 to-transparent mx-auto"></div>
            </div>
          </div>

          {/* Headline */}
          <div className="text-center px-2">
            <p className="text-sm text-slate-500 dark:text-slate-400 leading-relaxed font-medium">
              Building modern digital experiences with precision and creativity
            </p>
          </div>

          {/* Contact Section */}
          <div className="space-y-6">
            {/* Email */}
            <div className="text-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-3 px-4 py-3 rounded-lg text-sm text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 transition-all duration-200 group hover:bg-muted/30 font-semibold hover:underline"
              >
                <Mail className="w-4 h-4 text-primary/70 group-hover:text-primary transition-colors" />
                <span className="font-semibold"><EMAIL></span>
              </a>
            </div>
            
            {/* Social Links */}
            <div className="flex items-center justify-center gap-8">
              <a
                href="https://linkedin.com/in/cjjutba"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground/70 hover:text-[#0077B5] hover:scale-110 transition-all duration-200"
                title="LinkedIn"
              >
                <SiLinkedin className="w-6 h-6" />
              </a>
              <a
                href="https://github.com/cjjutba"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                title="GitHub"
              >
                <SiGithub className="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>

        {/* Footer Section */}
        <div className="space-y-4">

          {/* Optional: Subtle footer text */}
          {/* <div className="text-center pt-2">
            <div className="inline-flex items-center gap-2 px-3 py-2 rounded-full bg-emerald-50/50 dark:bg-emerald-950/30 border border-emerald-200/30 dark:border-emerald-800/30">
              <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
              <p className="text-xs text-emerald-700 dark:text-emerald-300 font-medium">
                Available for opportunities
              </p>
            </div>
          </div> */}

          {/* Download Resume Link */}
          <div className="text-center">
            <a
              href="/resume.pdf"
              download
              className="inline-flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-all duration-300 group font-medium hover:underline underline-offset-4"
            >
              <Download className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-200" />
              Download Resume
            </a>
          </div>
        
        </div>
      </div>
    </div>
  )
}